<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Str;
use App\Events\SyncUpdated;
use App\Models\Tickets;
use App\Models\Mealstubcomponents;
use App\Models\Inparkcurrencydetails;


class SyncController extends Controller
{
    public function sendToServer(Request $request) {
        $username = env('USER_NAME');
        $password = env('PASSWORD');
        $authHeader = 'Basic ' . base64_encode($username . ':' . $password);
        DB::beginTransaction();
        try {
            if(!empty($request->tickets)) {
                foreach($request->tickets as $ticket) {
                    DB::table('tickets')->UpdateOrInsert(
                    ['ticket_no'=>$ticket['ticket_no']],
                        [ 'branch_id' => $ticket['branch_id'],
                        'outlet_id' => $ticket['outlet_id'],
                        'status' => $ticket ['status'],
                        'expiry' => $ticket['expiry'] ?? null,]
                    );
                }

            }
            //MEAL STUB COMPONENT 
            if(!empty($request->mealstubcomponents)) {
                foreach ($request->mealstubcomponents as $meal_stub_component) {
                    DB::table('mealstubcomponents')->UpdateOrInsert(
                        ['REFERENCEID'=>$meal_stub_component['REFERENCEID']],

                        [
                            'branch_id' => $meal_stub_component['branch_id'],
                            'outlet_id' => $meal_stub_component['outlet_id'],
                            'product_id' => $meal_stub_component['product_id'],
                            'rowguid' => Str::uuid(),
                        ]
                    );

            }

        }
            if(!empty($request->inparkcurrencydetails)) {
                foreach ($request->inparkcurrencydetails as $inparkcurrencydetail) {
                    DB::table('inparkcurrencydetails')->UpdateOrInsert(
                        ['INPARKCURRENCYID'=>$inparkcurrencydetail['INPARKCURRENCYID']],

                        [
                            'branch_id' => $inparkcurrencydetail['branch_id'],
                            'outlet_id' => $inparkcurrencydetail['outlet_id'],
                            'STATUS' => $inparkcurrencydetail['STATUS'],
                            'product_id' => $inparkcurrencydetail['product_id'],
                            'rowguid' => Str::uuid(),
                        ]
                    );
                }
            }

            DB::commit();
            return response()->json(['success'=>'Data Added Successfully']);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('SendToServer Error:'.$e->getMessage());
                return response()->json(['error'=>'FAILED TO SYNC DATA'],500);
            }
            
        }
           public function getFromServer($branchId, $outletId)
    {
        return response()->json([
            'tickets' => DB::table('tickets')
                ->where('branch_id', $branchId)
                ->where('outlet_id', $outletId)
                ->whereIn('status', ['S', 'D']) // Sale or Distributed
                ->where(function ($q) {
                    $q->whereNull('expiry')->orWhere('expiry', '>=', now());
                })
                ->get(),

            'meal_stub_components' => DB::table('meal_stub_components')
                ->where('branch_id', $branchId)
                ->where('outlet_id', $outletId)
                ->get(),

            'in_park_currency_details' => DB::table('in_park_currency_details')
                ->where('branch_id', $branchId)
                ->where('outlet_id', $outletId)
                ->get()
        ]);
    }

     public function claim(Request $request)
    {

        $username = env('USER_NAME');
        $password = env('PASSWORD');
        $authHeader = 'Basic ' . base64_encode($username . ':' . $password);
        $request->validate([
            'ticket_no' => 'required|string',
            'branch_id' => 'required|integer',
            'outlet_id' => 'required|integer',
        ]);

        $ticket = DB::table('tickets')
            ->where('ticket_no', $request->ticket_no)
            ->first();

        if (!$ticket) {
            return response()->json(['error' => 'Ticket not found'], 404);
        }

        if ($ticket->status === 'V') {
            return response()->json(['error' => 'Ticket is void'], 400);
        }

        if ($ticket->status === 'U') {
            return response()->json(['error' => 'Ticket already used'], 400);
        }

        DB::table('tickets')
            ->where('ticket_no', $request->ticket_no)
            ->update([
                'status'      => 'U',
                'consumed_at' => now()
            ]);

        // Notify all POS that data changed
        broadcast(new \App\Events\SyncUpdated());

        return response()->json(['message' => 'Ticket claimed']);
    }

    /**
     * Void expired tickets
     */
    public function voidExpired()
    {   
         $username = env('USER_NAME');
        $password = env('PASSWORD');
        $authHeader = 'Basic ' . base64_encode($username . ':' . $password);
        DB::table('tickets')
            ->where('status', 'S')
            ->where('expiry', '<', now())
            ->update(['status' => 'V']);

        // Notify all POS
        broadcast(new \App\Events\SyncUpdated());

        return response()->json(['message' => 'Expired tickets voided']);
    }
}









    

